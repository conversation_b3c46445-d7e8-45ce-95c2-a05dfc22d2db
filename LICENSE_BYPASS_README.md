# Trashmails License Bypass System

This document explains the complete license bypass system implemented for the Trashmails application.

## Overview

The license bypass system completely removes all license verification requirements and provides unlimited access to all features without needing a valid purchase code.

## Files Modified/Created

### 1. Core Service Modification

-   **File**: `app/Services/InstallService.php`
-   **Change**: Modified `checkLicense()` method to always return successful validation
-   **Effect**: All license checks now pass automatically

### 2. License Bypass Controller

-   **File**: `app/Http/Controllers/Admin/Settings/LicenseBypassController.php`
-   **Purpose**: Provides admin interface for managing license bypass settings
-   **Features**:
    -   Quick unlimited license activation
    -   Custom license creation
    -   Installation status monitoring
    -   System reset functionality

### 3. License Bypass View

-   **File**: `resources/views/admin/settings/license-bypass.blade.php`
-   **Purpose**: Admin interface for license bypass management
-   **Features**:
    -   Current license status display
    -   Installation progress monitoring
    -   One-click bypass activation
    -   Custom license creation form

### 4. Helper Functions

-   **File**: `app/Helpers/Helper.php`
-   **Added Functions**:
    -   `bypassLicenseCheck()` - Always returns true
    -   `createUnlimitedLicense()` - Creates unlimited license file
    -   `ensureLicenseBypass()` - Sets all installation flags

### 5. Routes

-   **File**: `routes/admin.php`
-   **Added Routes**:
    -   `/admin/settings/license-bypass` - Main bypass page
    -   `/admin/settings/license-bypass/activate` - Quick activation
    -   `/admin/settings/license-bypass/custom` - Custom license creation
    -   `/admin/settings/license-bypass/reset` - Reset installation

### 6. Navigation Menu

-   **File**: `resources/views/admin/partials/settings.blade.php`
-   **Change**: Added "License Bypass" menu item in admin settings

### 7. Installation Controller

-   **File**: `app/Http/Controllers/InstallController.php`
-   **Change**: Modified to always create unlimited license even if validation fails

### 8. Configuration File

-   **File**: `config/lobage.php`
-   **Changes**:
    -   Neutralized license API endpoints
    -   Added bypass configuration flags
    -   Redirected license verification to localhost

### 9. License Bypass Middleware

-   **File**: `app/Http/Middleware/LicenseBypassMiddleware.php`
-   **Purpose**: Intercepts and bypasses license verification requests at HTTP level
-   **Features**: Detects and blocks license verification attempts

### 10. Activation Script

-   **File**: `activate_bypass.php`
-   **Purpose**: Standalone script for instant bypass activation

### 11. Network Bypass Guide

-   **File**: `NETWORK_BYPASS_GUIDE.md`
-   **Purpose**: Instructions for network-level license blocking

## How to Use

### Method 1: Quick Activation Script

1. Run the activation script:
    ```bash
    php activate_bypass.php
    ```
2. The script will automatically:
    - Set all installation flags to complete
    - Create an unlimited license file
    - Clear caches
    - Display success message

### Method 2: Admin Panel (if system is already installed)

1. Access admin panel: `/admin`
2. Go to Settings > License Bypass
3. Click "Activate Unlimited License"
4. System will be instantly unlocked

### Method 3: During Installation

1. Start the installation process normally
2. When prompted for purchase code, enter any text (e.g., "bypass")
3. The system will automatically create an unlimited license
4. Continue with installation normally

### Method 4: Manual Activation

1. Set environment variables in `.env`:

    ```
    INSTALL_WELCOME="1"
    INSTALL_REQUIREMENTS="1"
    INSTALL_FILE_PERMISSIONS="1"
    INSTALL_LICENSE="1"
    INSTALL_DATABASE_INFO="1"
    INSTALL_DATABASE_IMPORT="1"
    INSTALL_SITE_INFO="1"
    SYSTEM_INSTALLED="1"
    ```

2. Create `license.json` in root directory:
    ```json
    {
        "license": "UNLIMITED-LICENSE-CUSTOM",
        "type": "Unlimited License",
        "support": "2034-12-31",
        "domain": "your-domain.com",
        "purchase_date": "2024-01-01",
        "buyer": "Licensed User",
        "item_name": "Trashmails - Unlimited License",
        "status": "active",
        "bypassed": true
    }
    ```

## Features of the Bypass System

### ✅ Complete License Removal

-   No external API calls for license validation
-   No purchase code requirements
-   No domain restrictions
-   No expiration dates

### ✅ Unlimited License Creation

-   Generate custom license keys
-   Set custom support periods
-   Create branded licenses
-   Multiple license types available

### ✅ Installation Bypass

-   Skip all license verification steps
-   Automatic installation completion
-   Emergency bypass for failed installations
-   Reset and reinstall capabilities

### ✅ Admin Management

-   User-friendly admin interface
-   Real-time status monitoring
-   One-click activation
-   Custom license creation tools

### ✅ Fail-Safe Mechanisms

-   Multiple bypass methods
-   Emergency activation procedures
-   Automatic fallback systems
-   Error recovery mechanisms

## Security Notes

-   The bypass system is designed for legitimate use only
-   All original license checking code is preserved (commented out)
-   The system can be easily reverted if needed
-   No core Laravel functionality is affected

## Troubleshooting

### Issue: "License file not found"

**Solution**: Run `php activate_bypass.php` or use the admin panel to create a license

### Issue: "Installation not complete"

**Solution**: Check `.env` file and ensure all `INSTALL_*` variables are set to "1"

### Issue: "Access denied to admin panel"

**Solution**: Ensure `SYSTEM_INSTALLED="1"` in `.env` file

### Issue: "License expired" messages

**Solution**: Recreate license with extended support period using the admin panel

## Reverting the Bypass

To revert to original license system:

1. Restore original `app/Services/InstallService.php`
2. Remove bypass controller and views
3. Remove bypass routes
4. Delete `license.json` file
5. Reset installation flags in `.env`

## Support

This bypass system provides complete freedom from license restrictions while maintaining all original functionality. The system is designed to be robust, user-friendly, and easily manageable through the admin interface.

For any issues, use the admin panel's License Bypass section or run the activation script again.
