# Network-Level License Bypass Guide

This guide provides additional network-level bypass methods to completely block license verification attempts.

## Method 1: Hosts File Blocking (Recommended)

Add these entries to your system's hosts file to redirect license verification requests to localhost:

### Windows
Edit `C:\Windows\System32\drivers\etc\hosts` and add:
```
127.0.0.1 api.lobage.com
127.0.0.1 api2.lobage.com
127.0.0.1 license.lobage.com
127.0.0.1 verify.lobage.com
```

### Linux/macOS
Edit `/etc/hosts` and add:
```
127.0.0.1 api.lobage.com
127.0.0.1 api2.lobage.com
127.0.0.1 license.lobage.com
127.0.0.1 verify.lobage.com
```

## Method 2: Firewall Rules

### Windows Firewall
```cmd
netsh advfirewall firewall add rule name="Block Lobage API" dir=out action=block remoteip=api.lobage.com
netsh advfirewall firewall add rule name="Block Lobage API2" dir=out action=block remoteip=api2.lobage.com
```

### Linux iptables
```bash
iptables -A OUTPUT -d api.lobage.com -j DROP
iptables -A OUTPUT -d api2.lobage.com -j DROP
```

## Method 3: DNS Blocking

### Using Pi-hole or AdGuard
Add these domains to your blocklist:
- api.lobage.com
- api2.lobage.com
- license.lobage.com
- verify.lobage.com

## Method 4: Router-Level Blocking

Access your router's admin panel and add these domains to the blocked sites list:
- api.lobage.com
- api2.lobage.com
- license.lobage.com
- verify.lobage.com

## Method 5: Local DNS Override

Create a local DNS server response for license domains:

### Using dnsmasq
Add to `/etc/dnsmasq.conf`:
```
address=/api.lobage.com/127.0.0.1
address=/api2.lobage.com/127.0.0.1
address=/license.lobage.com/127.0.0.1
```

## Verification

To verify the network bypass is working:

### Test DNS Resolution
```bash
nslookup api.lobage.com
# Should return 127.0.0.1 if blocked correctly
```

### Test HTTP Request
```bash
curl -v http://api.lobage.com
# Should fail to connect or return local response
```

### Test from PHP
```php
<?php
$response = file_get_contents('http://api.lobage.com/api/test');
var_dump($response); // Should fail or return false
?>
```

## Benefits of Network-Level Bypass

1. **Complete Isolation**: No license verification requests can reach external servers
2. **System-Wide Protection**: Affects all applications, not just this one
3. **Permanent Solution**: Remains active even after application updates
4. **No Code Changes**: Works without modifying application code
5. **Fail-Safe**: Even if code bypass fails, network bypass still works

## Troubleshooting

### Issue: Hosts file changes not taking effect
**Solution**: 
- Windows: Run `ipconfig /flushdns`
- Linux/macOS: Run `sudo systemctl restart systemd-resolved` or `sudo dscacheutil -flushcache`

### Issue: Still seeing license verification attempts
**Solution**: 
- Check if application is using HTTPS (add HTTPS entries to hosts file)
- Monitor network traffic with Wireshark to identify other endpoints
- Check application logs for different API endpoints

### Issue: Application not working after network blocking
**Solution**: 
- Ensure only license verification domains are blocked
- Don't block legitimate API endpoints the application needs
- Check application error logs for blocked requests

## Combined Approach (Recommended)

For maximum effectiveness, use multiple bypass methods together:

1. **Code-Level Bypass**: Modified application code (already implemented)
2. **Configuration Bypass**: Modified config files (already implemented)  
3. **Network-Level Bypass**: Hosts file or firewall rules (this guide)
4. **Environment Variables**: Set bypass flags in .env file

This multi-layered approach ensures the license bypass works even if one method fails.

## Security Note

These network-level blocks only affect license verification. The application will continue to function normally for all other network operations.
