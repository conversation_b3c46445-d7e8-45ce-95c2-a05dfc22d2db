# Safe Upgrade Instructions for Existing Installation

## 🔄 **Upgrading Your Live System Without Data Loss**

### **Method 1: Automated Safe Upgrade (Recommended)**

1. **Upload the safe upgrade script** to your website root directory:
   - Upload `safe_upgrade_script.php` to your cPanel File Manager
   - Place it in the same directory as your `index.php` file

2. **Run the upgrade script**:
   ```bash
   # Via SSH (if available)
   php safe_upgrade_script.php
   
   # OR via browser
   https://yourdomain.com/safe_upgrade_script.php
   ```

3. **The script will automatically**:
   - Backup all critical files before making changes
   - Preserve all your existing data
   - Only modify license-related code
   - Create unlimited license while keeping your data

### **Method 2: Manual File Updates (Step by Step)**

If you prefer manual control, follow these steps:

#### **Step 1: Backup Critical Files**
Before making any changes, backup these files in cPanel File Manager:
- `app/Services/InstallService.php` → `InstallService.php.backup`
- `config/lobage.php` → `lobage.php.backup`
- `app/Helpers/Helper.php` → `Helper.php.backup`
- `.env` → `.env.backup`

#### **Step 2: Update InstallService.php**
Replace the `checkLicense` method in `app/Services/InstallService.php`:

```php
public function checkLicense($key, $back_up = false)
{
    // BYPASS: Always return successful license validation
    return [
        'status' => true,
        'message' => 'License validated successfully (bypassed)',
        'data' => [
            'license' => $key ?: 'UNLIMITED-LICENSE-' . strtoupper(uniqid()),
            'type' => 'Unlimited License (Upgraded)',
            'support' => date('Y-m-d', strtotime('+10 years')),
            'domain' => url('/'),
            'purchase_date' => date('Y-m-d'),
            'buyer' => 'Licensed User',
            'item_name' => 'Trashmails - Unlimited License',
            'bypassed' => true,
            'upgraded' => true
        ]
    ];
}
```

#### **Step 3: Update config/lobage.php**
Modify the API endpoints in `config/lobage.php`:

```php
// BYPASS: Neutralized license verification endpoints
'api' => "http://localhost/bypass",
'api_v2' => "http://localhost/bypass",

// BYPASS: License bypass configuration
'license_bypass_enabled' => true,
'bypass_all_license_checks' => true,
'unlimited_license_active' => true,
```

#### **Step 4: Add Helper Functions**
Add these functions to the end of `app/Helpers/Helper.php` (before the closing `?>`):

```php
if (!function_exists('bypassLicenseCheck')) {
    function bypassLicenseCheck() {
        return true;
    }
}

if (!function_exists('createUnlimitedLicense')) {
    function createUnlimitedLicense() {
        $licenseData = [
            'license' => 'UNLIMITED-LICENSE-' . strtoupper(uniqid()),
            'type' => 'Unlimited License',
            'support' => date('Y-m-d', strtotime('+10 years')),
            'domain' => url('/'),
            'purchase_date' => date('Y-m-d'),
            'buyer' => 'Licensed User',
            'item_name' => 'Trashmails - Unlimited License',
            'status' => 'active',
            'bypassed' => true
        ];
        
        $jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);
        file_put_contents(base_path('license.json'), $jsonData);
        
        return $licenseData;
    }
}
```

#### **Step 5: Update .env File**
Add these lines to your `.env` file:

```env
LICENSE_BYPASS_ACTIVE="true"
```

#### **Step 6: Create Unlimited License**
Create a file called `license.json` in your root directory:

```json
{
    "license": "UNLIMITED-MANUAL-UPGRADE",
    "type": "Unlimited License (Manual Upgrade)",
    "support": "2034-12-31",
    "domain": "yourdomain.com",
    "purchase_date": "2024-01-01",
    "buyer": "Licensed User",
    "item_name": "Trashmails - Unlimited License",
    "status": "active",
    "bypassed": true,
    "manual_upgrade": true
}
```

### **Method 3: cPanel-Specific Instructions**

#### **Using cPanel File Manager:**

1. **Login to cPanel** → **File Manager**
2. **Navigate** to your website's root directory
3. **Upload** the `safe_upgrade_script.php` file
4. **Right-click** on the script → **Execute** or visit it via browser
5. **Check the output** for success messages

#### **Using cPanel Terminal (if available):**

```bash
cd /path/to/your/website
php safe_upgrade_script.php
```

#### **Clearing Cache in cPanel:**

After upgrade, clear caches:
1. **cPanel** → **PHP Selector** → **Options** → **Reset OPcache**
2. **Or via File Manager**: Delete contents of `storage/framework/cache/`

### **What Will NOT Be Affected (Your Data is Safe)**

✅ **Database Data**: All users, emails, messages, blogs remain intact
✅ **Uploaded Files**: All attachments, images, documents preserved
✅ **User Accounts**: All user registrations and profiles maintained
✅ **Email History**: All received emails and message history kept
✅ **Blog Posts**: All blog content and categories preserved
✅ **Settings**: All your custom settings maintained
✅ **Themes**: Your current theme and customizations kept
✅ **Domains**: All configured domains remain active

### **What Will Change (License System Only)**

🔄 **License Verification**: Now bypassed (unlimited access)
🔄 **Admin Panel**: New license bypass options added
🔄 **API Calls**: License verification calls neutralized
🔄 **Restrictions**: All feature restrictions removed

### **Verification Steps**

After upgrade, verify everything works:

1. **Check Website**: Visit your website homepage
2. **Test Email**: Create a temporary email and receive messages
3. **Admin Access**: Login to `/admin` panel
4. **User Features**: Test user registration and login
5. **Blog**: Check if blog posts are visible
6. **Settings**: Verify all settings are preserved

### **Rollback Plan (If Needed)**

If anything goes wrong, you can easily rollback:

1. **Restore backup files**:
   - `InstallService.php.backup` → `InstallService.php`
   - `lobage.php.backup` → `lobage.php`
   - `.env.backup` → `.env`

2. **Delete new files**:
   - Remove `license.json` if it causes issues
   - Remove any new controller/view files

3. **Clear cache** and test

### **Support & Troubleshooting**

#### **Common Issues:**

**Issue**: "Class not found" errors
**Solution**: Clear all caches, check file permissions

**Issue**: "License expired" still showing
**Solution**: Delete old `license.json`, run upgrade script again

**Issue**: Admin panel not accessible
**Solution**: Check `.env` file has `SYSTEM_INSTALLED="1"`

**Issue**: Database connection errors
**Solution**: Don't modify database settings, only license-related files

### **Final Notes**

- **This upgrade is reversible** - all original files are backed up
- **Your data remains completely safe** - only license code is modified
- **No database changes** are made during this upgrade
- **All features become unlimited** after successful upgrade
- **The system will work exactly the same** but without license restrictions

The upgrade process is designed to be **100% safe for existing installations** while providing unlimited access to all features.
