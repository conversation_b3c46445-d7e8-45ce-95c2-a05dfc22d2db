<?php
/**
 * License Bypass Activation Script
 * Run this script to instantly activate unlimited license bypass
 * 
 * Usage: php activate_bypass.php
 */

echo "=== Trashmails License Bypass Activation ===\n";
echo "Starting license bypass activation...\n\n";

// Function to update .env file
function updateEnvFile($key, $value) {
    $envFile = '.env';
    
    if (!file_exists($envFile)) {
        echo "Error: .env file not found!\n";
        return false;
    }
    
    $currentEnv = file_get_contents($envFile);
    $value = '"' . trim($value) . '"';
    $pattern = "/^{$key}=(.*)$/m";
    
    if (preg_match($pattern, $currentEnv, $matches)) {
        $newEnv = preg_replace($pattern, "{$key}={$value}", $currentEnv);
    } else {
        $newEnv = $currentEnv . "\n{$key}={$value}";
    }
    
    file_put_contents($envFile, $newEnv);
    echo "✓ Updated {$key} = {$value}\n";
    return true;
}

// Function to create unlimited license
function createUnlimitedLicense() {
    $licenseData = [
        'license' => 'UNLIMITED-LICENSE-' . strtoupper(uniqid()),
        'type' => 'Unlimited License',
        'support' => date('Y-m-d', strtotime('+10 years')),
        'domain' => 'localhost', // Will be updated when accessed
        'purchase_date' => date('Y-m-d'),
        'buyer' => 'Licensed User',
        'item_name' => 'Trashmails - Unlimited License',
        'status' => 'active',
        'bypassed' => true,
        'created_by_script' => true,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);
    file_put_contents('license.json', $jsonData);
    
    echo "✓ Created unlimited license file (license.json)\n";
    echo "  License Key: " . $licenseData['license'] . "\n";
    echo "  Support Until: " . $licenseData['support'] . "\n";
    
    return $licenseData;
}

try {
    // Step 1: Update all installation flags
    echo "Step 1: Setting installation flags...\n";
    updateEnvFile('INSTALL_WELCOME', '1');
    updateEnvFile('INSTALL_REQUIREMENTS', '1');
    updateEnvFile('INSTALL_FILE_PERMISSIONS', '1');
    updateEnvFile('INSTALL_LICENSE', '1');
    updateEnvFile('INSTALL_DATABASE_INFO', '1');
    updateEnvFile('INSTALL_DATABASE_IMPORT', '1');
    updateEnvFile('INSTALL_SITE_INFO', '1');
    updateEnvFile('SYSTEM_INSTALLED', '1');
    
    echo "\nStep 2: Creating unlimited license...\n";
    $license = createUnlimitedLicense();
    
    echo "\nStep 3: Clearing cache (if possible)...\n";
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "✓ OPCache cleared\n";
    }
    
    // Try to clear Laravel cache
    if (file_exists('artisan')) {
        $output = [];
        $return_var = 0;
        exec('php artisan cache:clear 2>&1', $output, $return_var);
        if ($return_var === 0) {
            echo "✓ Laravel cache cleared\n";
        }
        
        exec('php artisan config:clear 2>&1', $output, $return_var);
        if ($return_var === 0) {
            echo "✓ Laravel config cache cleared\n";
        }
    }
    
    echo "\n=== ACTIVATION COMPLETE ===\n";
    echo "✅ License bypass has been successfully activated!\n";
    echo "✅ System is now fully unlocked with unlimited license\n";
    echo "✅ All installation steps have been marked as complete\n\n";
    
    echo "Next Steps:\n";
    echo "1. Access your admin panel at: /admin\n";
    echo "2. Go to Settings > License Bypass to manage license settings\n";
    echo "3. The system is now ready to use without any license restrictions\n\n";
    
    echo "License Details:\n";
    echo "- License Key: " . $license['license'] . "\n";
    echo "- Type: " . $license['type'] . "\n";
    echo "- Support Until: " . $license['support'] . "\n";
    echo "- Status: Active (Bypassed)\n\n";
    
    echo "Note: You can delete this script (activate_bypass.php) after successful activation.\n";
    
} catch (Exception $e) {
    echo "\n❌ ERROR: " . $e->getMessage() . "\n";
    echo "Please check file permissions and try again.\n";
    exit(1);
}

echo "\n=== Script completed successfully ===\n";
?>
