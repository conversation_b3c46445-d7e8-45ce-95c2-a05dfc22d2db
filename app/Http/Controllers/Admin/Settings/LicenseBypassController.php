<?php

namespace App\Http\Controllers\Admin\Settings;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;

class LicenseBypassController extends Controller
{
    public function index()
    {
        // Check if license.json exists
        $licenseExists = File::exists(base_path('license.json'));
        $licenseData = [];
        
        if ($licenseExists) {
            $licenseData = json_decode(File::get(base_path('license.json')), true);
        }
        
        // Check installation status
        $installationStatus = [
            'INSTALL_WELCOME' => env('INSTALL_WELCOME', '0'),
            'INSTALL_REQUIREMENTS' => env('INSTALL_REQUIREMENTS', '0'),
            'INSTALL_FILE_PERMISSIONS' => env('INSTALL_FILE_PERMISSIONS', '0'),
            'INSTALL_LICENSE' => env('INSTALL_LICENSE', '0'),
            'INSTALL_DATABASE_INFO' => env('INSTALL_DATABASE_INFO', '0'),
            'INSTALL_DATABASE_IMPORT' => env('INSTALL_DATABASE_IMPORT', '0'),
            'INSTALL_SITE_INFO' => env('INSTALL_SITE_INFO', '0'),
            'SYSTEM_INSTALLED' => env('SYSTEM_INSTALLED', '0'),
        ];
        
        return view('admin.settings.license-bypass', compact('licenseExists', 'licenseData', 'installationStatus'));
    }
    
    public function activateBypass(Request $request)
    {
        try {
            // Create unlimited license
            $licenseData = createUnlimitedLicense();
            
            // Ensure all installation flags are set
            ensureLicenseBypass();
            
            return back()->with('success', 'License bypass activated successfully! System is now fully unlocked.');
            
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to activate bypass: ' . $e->getMessage()]);
        }
    }
    
    public function createCustomLicense(Request $request)
    {
        $request->validate([
            'license_key' => 'required|string',
            'license_type' => 'required|string',
            'buyer_name' => 'required|string',
            'support_years' => 'required|integer|min:1|max:50'
        ]);
        
        try {
            $licenseData = [
                'license' => $request->license_key,
                'type' => $request->license_type,
                'support' => date('Y-m-d', strtotime('+' . $request->support_years . ' years')),
                'domain' => url('/'),
                'purchase_date' => date('Y-m-d'),
                'buyer' => $request->buyer_name,
                'item_name' => 'Trashmails - Custom License',
                'status' => 'active',
                'bypassed' => true,
                'custom' => true
            ];
            
            // Create license.json file
            $jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);
            File::put(base_path('license.json'), $jsonData);
            
            // Ensure installation is complete
            ensureLicenseBypass();
            
            return back()->with('success', 'Custom license created successfully!');
            
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create custom license: ' . $e->getMessage()]);
        }
    }
    
    public function resetInstallation(Request $request)
    {
        try {
            // Reset all installation flags
            updateEnvFile('INSTALL_WELCOME', '0');
            updateEnvFile('INSTALL_REQUIREMENTS', '0');
            updateEnvFile('INSTALL_FILE_PERMISSIONS', '0');
            updateEnvFile('INSTALL_LICENSE', '0');
            updateEnvFile('INSTALL_DATABASE_INFO', '0');
            updateEnvFile('INSTALL_DATABASE_IMPORT', '0');
            updateEnvFile('INSTALL_SITE_INFO', '0');
            updateEnvFile('SYSTEM_INSTALLED', '0');
            
            // Remove license file
            if (File::exists(base_path('license.json'))) {
                File::delete(base_path('license.json'));
            }
            
            return back()->with('success', 'Installation reset successfully! You can now reinstall the system.');
            
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reset installation: ' . $e->getMessage()]);
        }
    }
}
