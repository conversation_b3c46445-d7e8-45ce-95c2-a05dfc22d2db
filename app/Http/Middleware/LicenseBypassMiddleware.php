<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LicenseBypassMiddleware
{
    /**
     * Handle an incoming request.
     * This middleware ensures license bypass is always active
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Force license bypass environment variables if not set
        if (!env('LICENSE_BYPASS_ACTIVE')) {
            putenv('LICENSE_BYPASS_ACTIVE=true');
        }
        
        // Check if this is a license verification request and bypass it
        if ($this->isLicenseVerificationRequest($request)) {
            return $this->returnBypassedLicenseResponse();
        }
        
        // Ensure license bypass is active in config
        config(['lobage.license_bypass_enabled' => true]);
        config(['lobage.bypass_all_license_checks' => true]);
        config(['lobage.unlimited_license_active' => true]);
        
        return $next($request);
    }
    
    /**
     * Check if this is a license verification request
     */
    private function isLicenseVerificationRequest(Request $request): bool
    {
        $uri = $request->getRequestUri();
        $host = $request->getHost();
        
        // Check for license verification patterns
        $licensePatterns = [
            '/license',
            '/verify',
            '/check',
            '/validate',
            '/activation',
            '/purchase'
        ];
        
        // Check for license verification hosts
        $licenseHosts = [
            'api.lobage.com',
            'api2.lobage.com',
            'license.lobage.com'
        ];
        
        foreach ($licensePatterns as $pattern) {
            if (strpos($uri, $pattern) !== false) {
                return true;
            }
        }
        
        foreach ($licenseHosts as $licenseHost) {
            if (strpos($host, $licenseHost) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Return a bypassed license response
     */
    private function returnBypassedLicenseResponse(): Response
    {
        $bypassedResponse = [
            'status' => true,
            'message' => 'License verified successfully (middleware bypass)',
            'data' => [
                'license' => 'MIDDLEWARE-BYPASS-' . strtoupper(uniqid()),
                'type' => 'Unlimited License (Middleware Bypass)',
                'support' => date('Y-m-d', strtotime('+10 years')),
                'domain' => request()->getHost(),
                'purchase_date' => date('Y-m-d'),
                'buyer' => 'Licensed User (Middleware)',
                'item_name' => 'Trashmails - Unlimited License',
                'bypass_method' => 'middleware',
                'middleware_bypass' => true
            ]
        ];
        
        return response()->json($bypassedResponse);
    }
}
