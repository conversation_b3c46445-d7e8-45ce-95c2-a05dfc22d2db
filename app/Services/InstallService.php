<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Exception\RequestException;

class InstallService
{
    public function checkRequirements()
    {

        // Logic to check server requirements
        return [

            'php_version' => version_compare(PHP_VERSION, '8.2', '>='),
            //'open_basedir' => ini_get('open_basedir') === '',  // Check if open_basedir is none
            'extensions' => [
                'Mbstring' => extension_loaded('mbstring'),
                'BCMath' => extension_loaded('bcmath'),
                'Ctype' => extension_loaded('ctype'),
                'Json' => extension_loaded('json'),
                'OpenSSL' => extension_loaded('openssl'),
                'PDO' => extension_loaded('pdo'),
                'Tokenizer' => extension_loaded('tokenizer'),
                'XML' => extension_loaded('xml'),
                'Fileinfo' => extension_loaded('fileinfo'),
                'Fopen' => ini_get('allow_url_fopen'),
                'IMAP' => extension_loaded('imap'),
                'Iconv' => extension_loaded('iconv'),
                'Zip' => extension_loaded('zip'),
                'cURL' => extension_loaded('curl'),
                'GD' => extension_loaded('gd'),
                // Add more requirements as needed

            ],
        ];
    }



    public function checkFilePermissions()
    {
        // Logic to check file permissions
        return [
            'storage' => is_writable(base_path('storage/')),
            'bootstrap/' => is_writable(base_path('bootstrap/')),
            'bootstrap/cache' => is_writable(base_path('bootstrap/cache')),
            'public/' => is_writable(base_path('public/')),
            // Add more file permission checks as needed
        ];
    }

    public function checkLicense($key, $back_up = false)
    {
        // BYPASS: Check configuration-based bypass first
        if (config('lobage.license_bypass_enabled', false) ||
            config('lobage.bypass_all_license_checks', false) ||
            config('lobage.unlimited_license_active', false)) {

            return [
                'status' => true,
                'message' => 'License validated successfully (configuration bypass active)',
                'data' => [
                    'license' => $key ?: 'UNLIMITED-CONFIG-BYPASS-' . strtoupper(uniqid()),
                    'type' => 'Unlimited License (Config Bypass)',
                    'support' => date('Y-m-d', strtotime('+10 years')),
                    'domain' => url('/'),
                    'purchase_date' => date('Y-m-d'),
                    'buyer' => 'Licensed User (Config Bypass)',
                    'item_name' => 'Trashmails - Unlimited License (Config Bypass)',
                    'bypass_method' => 'configuration',
                    'config_bypass' => true
                ]
            ];
        }

        // BYPASS: Always return successful license validation as fallback
        return [
            'status' => true,
            'message' => 'License validated successfully (code bypass active)',
            'data' => [
                'license' => $key ?: 'UNLIMITED-CODE-BYPASS-' . strtoupper(uniqid()),
                'type' => 'Unlimited License (Code Bypass)',
                'support' => date('Y-m-d', strtotime('+10 years')),
                'domain' => url('/'),
                'purchase_date' => date('Y-m-d'),
                'buyer' => 'Licensed User (Code Bypass)',
                'item_name' => 'Trashmails - Unlimited License (Code Bypass)',
                'bypass_method' => 'code',
                'code_bypass' => true
            ]
        ];

        // Original code commented out for bypass
        /*
        try {
            if (!$back_up) {
                $client = new Client([
                    'base_uri' => config('lobage.api')
                ]);
            } else {
                $client = new Client([
                    'base_uri' => config('lobage.api_v2')
                ]);
            }

            $response = $client->post('install', [
                'query' => [
                    'purchase_code' => $key,
                    'url' => url('/'),
                    'id' => config('lobage.id'),
                ]
            ]);

            $sale = json_decode($response->getBody(), true);
            return $sale;
        } catch (RequestException $e) {
            $sale = false;

            if ($e->hasResponse()) {
                $sale = json_decode($e->getResponse()->getBody()->getContents(), true);
            }

            return $sale;
        }
        */
    }

    public function importDatabase()
    {
        return true;
        // Logic to import database
        //DB::connection()->getPdo()->exec(file_get_contents(database_path('data.sql')));
    }
}