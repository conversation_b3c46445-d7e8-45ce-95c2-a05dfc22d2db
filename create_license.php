<?php
/**
 * Quick License Creator
 * Creates unlimited license.json file
 */

echo "Creating unlimited license file...\n";

$licenseData = [
    "license" => "QUICK-UNLIMITED-" . strtoupper(uniqid()),
    "type" => "Unlimited License (Quick Creation)",
    "support" => date('Y-m-d', strtotime('+10 years')),
    "domain" => $_SERVER['HTTP_HOST'] ?? 'localhost',
    "purchase_date" => date('Y-m-d'),
    "buyer" => "Licensed User",
    "item_name" => "Trashmails - Unlimited License",
    "status" => "active",
    "bypassed" => true,
    "quick_creation" => true,
    "created_date" => date('Y-m-d H:i:s')
];

$jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);

if (file_put_contents('license.json', $jsonData)) {
    echo "✅ License file created successfully!\n";
    echo "📄 File: license.json\n";
    echo "🔑 License Key: " . $licenseData['license'] . "\n";
    echo "📅 Support Until: " . $licenseData['support'] . "\n";
    echo "🌐 Domain: " . $licenseData['domain'] . "\n";
    
    // Display the content
    echo "\n📋 License file content:\n";
    echo $jsonData;
    
} else {
    echo "❌ Failed to create license file!\n";
    echo "Check file permissions in your hosting panel.\n";
}

echo "\n\n🎯 Next steps:\n";
echo "1. Check that license.json file exists in your root directory\n";
echo "2. You can now proceed with the full upgrade if needed\n";
echo "3. Delete this create_license.php file after use\n";
?>
