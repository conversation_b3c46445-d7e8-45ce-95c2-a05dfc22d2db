<?php
// Debug script to show PHP errors
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>Debug Information</h1>";

// Check PHP version
echo "<h2>PHP Version</h2>";
echo "PHP Version: " . phpversion() . "<br>";

// Check if main files exist
echo "<h2>File Check</h2>";
$files = [
    'index.php',
    'license.json',
    '.env',
    'app/Services/InstallService.php',
    'config/lobage.php',
    'bootstrap/app.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ {$file} - EXISTS<br>";
    } else {
        echo "❌ {$file} - MISSING<br>";
    }
}

// Check license.json content
echo "<h2>License File Content</h2>";
if (file_exists('license.json')) {
    $license = file_get_contents('license.json');
    echo "<pre>" . htmlspecialchars($license) . "</pre>";
    
    $decoded = json_decode($license, true);
    if ($decoded) {
        echo "✅ License JSON is valid<br>";
    } else {
        echo "❌ License JSON is invalid: " . json_last_error_msg() . "<br>";
    }
} else {
    echo "❌ license.json file not found<br>";
}

// Check .env file
echo "<h2>Environment File</h2>";
if (file_exists('.env')) {
    echo "✅ .env file exists<br>";
    $env = file_get_contents('.env');
    // Don't show sensitive data, just check key variables
    if (strpos($env, 'SYSTEM_INSTALLED') !== false) {
        echo "✅ SYSTEM_INSTALLED found in .env<br>";
    } else {
        echo "❌ SYSTEM_INSTALLED not found in .env<br>";
    }
} else {
    echo "❌ .env file not found<br>";
}

// Test basic Laravel functionality
echo "<h2>Laravel Test</h2>";
try {
    if (file_exists('vendor/autoload.php')) {
        echo "✅ Composer autoload exists<br>";
    } else {
        echo "❌ Composer autoload missing<br>";
    }
    
    if (file_exists('bootstrap/app.php')) {
        echo "✅ Bootstrap file exists<br>";
    } else {
        echo "❌ Bootstrap file missing<br>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Check permissions
echo "<h2>File Permissions</h2>";
$dirs = ['storage', 'bootstrap/cache', 'public'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ {$dir} - WRITABLE<br>";
        } else {
            echo "❌ {$dir} - NOT WRITABLE<br>";
        }
    } else {
        echo "❌ {$dir} - DIRECTORY MISSING<br>";
    }
}

echo "<h2>Memory and Limits</h2>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Upload Max Size: " . ini_get('upload_max_filesize') . "<br>";

echo "<h2>Current Directory Contents</h2>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        echo $file . "<br>";
    }
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Check the error logs in cPanel for specific error messages</li>";
echo "<li>Look for any ❌ items above and fix them</li>";
echo "<li>If license.json is invalid, recreate it</li>";
echo "<li>Check file permissions if directories are not writable</li>";
echo "</ol>";
?>
