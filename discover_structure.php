<?php
// Discover the real application structure
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Application Structure Discovery</h1>";

// Function to scan directory recursively
function scanDirectory($dir, $maxDepth = 2, $currentDepth = 0) {
    $result = [];
    if ($currentDepth >= $maxDepth) return $result;
    
    if (is_dir($dir)) {
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $path = $dir . '/' . $file;
                if (is_dir($path)) {
                    $result[$file] = scanDirectory($path, $maxDepth, $currentDepth + 1);
                } else {
                    $result[] = $file;
                }
            }
        }
    }
    return $result;
}

// Check main index.php content
echo "<h2>📄 Index.php Analysis</h2>";
if (file_exists('index.php')) {
    $indexContent = file_get_contents('index.php');
    $preview = substr($indexContent, 0, 500);
    echo "<pre>" . htmlspecialchars($preview) . "...</pre>";
    
    // Look for framework indicators
    if (strpos($indexContent, 'Laravel') !== false) {
        echo "🔍 Framework: Laravel detected<br>";
    } elseif (strpos($indexContent, 'CodeIgniter') !== false) {
        echo "🔍 Framework: CodeIgniter detected<br>";
    } elseif (strpos($indexContent, 'include') !== false || strpos($indexContent, 'require') !== false) {
        echo "🔍 Framework: Custom PHP application<br>";
    } else {
        echo "🔍 Framework: Unknown<br>";
    }
}

// Look for configuration files
echo "<h2>⚙️ Configuration Files</h2>";
$configFiles = [
    'config.php',
    'configuration.php',
    'settings.php',
    'app_config.php',
    'database.php',
    'db_config.php',
    'constants.php'
];

foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "✅ Found: {$file}<br>";
    }
}

// Look for common directories
echo "<h2>📁 Directory Structure</h2>";
$structure = scanDirectory('.', 3);
foreach ($structure as $key => $value) {
    if (is_array($value)) {
        echo "<strong>{$key}/</strong><br>";
        foreach ($value as $subKey => $subValue) {
            if (is_array($subValue)) {
                echo "&nbsp;&nbsp;📁 {$subKey}/<br>";
                foreach ($subValue as $item) {
                    if (!is_array($item)) {
                        echo "&nbsp;&nbsp;&nbsp;&nbsp;📄 {$item}<br>";
                    }
                }
            } else {
                echo "&nbsp;&nbsp;📄 {$subValue}<br>";
            }
        }
    }
}

// Look for license-related files
echo "<h2>🔑 License-Related Files Search</h2>";
function findLicenseFiles($dir = '.') {
    $licenseFiles = [];
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $filename = $file->getFilename();
            $filepath = $file->getPathname();
            
            // Look for license-related files
            if (stripos($filename, 'license') !== false || 
                stripos($filename, 'activation') !== false ||
                stripos($filename, 'verify') !== false ||
                stripos($filename, 'check') !== false) {
                $licenseFiles[] = $filepath;
            }
            
            // Look for files that might contain license checking code
            if (pathinfo($filename, PATHINFO_EXTENSION) === 'php') {
                $content = file_get_contents($filepath);
                if (stripos($content, 'license') !== false && 
                    (stripos($content, 'verify') !== false || 
                     stripos($content, 'check') !== false ||
                     stripos($content, 'validation') !== false)) {
                    $licenseFiles[] = $filepath . ' (contains license code)';
                }
            }
        }
    }
    
    return array_unique($licenseFiles);
}

try {
    $licenseFiles = findLicenseFiles();
    if (!empty($licenseFiles)) {
        foreach ($licenseFiles as $file) {
            echo "🔍 {$file}<br>";
        }
    } else {
        echo "❌ No license-related files found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error scanning for license files: " . $e->getMessage() . "<br>";
}

// Check for database connection files
echo "<h2>🗄️ Database Configuration</h2>";
$dbFiles = glob('*{config,db,database}*.php', GLOB_BRACE);
foreach ($dbFiles as $file) {
    echo "📄 Found DB file: {$file}<br>";
    $content = file_get_contents($file);
    if (stripos($content, 'mysql') !== false || stripos($content, 'database') !== false) {
        echo "&nbsp;&nbsp;✅ Contains database configuration<br>";
    }
}

// Look for admin files
echo "<h2>👤 Admin Files</h2>";
$adminFiles = glob('*admin*.php');
$adminDirs = glob('*admin*', GLOB_ONLYDIR);

foreach ($adminFiles as $file) {
    echo "📄 Admin file: {$file}<br>";
}

foreach ($adminDirs as $dir) {
    echo "📁 Admin directory: {$dir}/<br>";
}

// Check for includes/requires in index.php
echo "<h2>🔗 Application Entry Points</h2>";
if (file_exists('index.php')) {
    $indexContent = file_get_contents('index.php');
    preg_match_all('/(include|require)(_once)?\s*\(?[\'"]([^\'"]+)[\'"]/', $indexContent, $matches);
    
    if (!empty($matches[3])) {
        echo "📄 Index.php includes:<br>";
        foreach ($matches[3] as $include) {
            echo "&nbsp;&nbsp;→ {$include}<br>";
            if (file_exists($include)) {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;✅ File exists<br>";
            } else {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;❌ File missing<br>";
            }
        }
    }
}

echo "<hr>";
echo "<h2>🎯 Next Steps</h2>";
echo "<p>Based on this analysis, I'll provide the correct bypass method for your specific application structure.</p>";
echo "<p><strong>Share this output so I can create the proper bypass solution!</strong></p>";
?>