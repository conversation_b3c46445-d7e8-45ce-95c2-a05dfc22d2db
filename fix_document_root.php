<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Document Root Fix Tool</h1>";

echo "<h2>📋 Current Situation Analysis</h2>";
echo "Web server is looking for: <code>/home/<USER>/public_html/public/index.php</code><br>";
echo "But your file is at: <code>/home/<USER>/public_html/index.php</code><br><br>";

// Check current structure
echo "<h2>📁 Current File Structure</h2>";
$currentDir = '/home/<USER>/public_html';
$publicDir = '/home/<USER>/public_html/public';

echo "Current directory contents:<br>";
if (is_dir($currentDir)) {
    $files = scandir($currentDir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $path = $currentDir . '/' . $file;
            if (is_dir($path)) {
                echo "📁 {$file}/<br>";
            } else {
                echo "📄 {$file}<br>";
            }
        }
    }
}

echo "<br>Public subdirectory exists: ";
if (is_dir($publicDir)) {
    echo "✅ YES<br>";
    echo "Public subdirectory contents:<br>";
    $files = scandir($publicDir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo "&nbsp;&nbsp;📄 {$file}<br>";
        }
    }
} else {
    echo "❌ NO<br>";
}

// Action buttons
if ($_POST['action'] ?? false) {
    $action = $_POST['action'];
    
    echo "<div style='background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🔄 Executing: {$action}</h3>";
    
    switch ($action) {
        case 'create_public_dir':
            createPublicDirectory();
            break;
            
        case 'move_files':
            moveFilesToPublic();
            break;
            
        case 'create_htaccess':
            createHtaccess();
            break;
            
        case 'full_fix':
            createPublicDirectory();
            moveFilesToPublic();
            createHtaccess();
            echo "<h3>🎉 DOCUMENT ROOT FIX COMPLETED!</h3>";
            echo "<p>✅ Your website should now work properly!</p>";
            echo "<p>🔗 Try accessing your website now.</p>";
            break;
    }
    
    echo "</div>";
}

function createPublicDirectory() {
    $publicDir = '/home/<USER>/public_html/public';
    
    if (!is_dir($publicDir)) {
        if (mkdir($publicDir, 0755, true)) {
            echo "✅ Created public/ directory<br>";
        } else {
            echo "❌ Failed to create public/ directory<br>";
            return false;
        }
    } else {
        echo "✅ Public/ directory already exists<br>";
    }
    
    return true;
}

function moveFilesToPublic() {
    $sourceDir = '/home/<USER>/public_html';
    $targetDir = '/home/<USER>/public_html/public';
    
    // Files that should be in the public directory
    $publicFiles = [
        'index.php',
        'assets',
        'uploads',
        'temp',
        'images',
        'favicon.ico',
        'robots.txt',
        'sitemap.xml',
        '.htaccess'
    ];
    
    foreach ($publicFiles as $file) {
        $sourcePath = $sourceDir . '/' . $file;
        $targetPath = $targetDir . '/' . $file;
        
        if (file_exists($sourcePath) && !file_exists($targetPath)) {
            if (is_dir($sourcePath)) {
                if (rename($sourcePath, $targetPath)) {
                    echo "✅ Moved directory: {$file}<br>";
                } else {
                    echo "❌ Failed to move directory: {$file}<br>";
                }
            } else {
                if (rename($sourcePath, $targetPath)) {
                    echo "✅ Moved file: {$file}<br>";
                } else {
                    echo "❌ Failed to move file: {$file}<br>";
                }
            }
        } elseif (file_exists($targetPath)) {
            echo "⚠️ Already exists in public/: {$file}<br>";
        } else {
            echo "⚠️ Source not found: {$file}<br>";
        }
    }
}

function createHtaccess() {
    $htaccessContent = 'RewriteEngine On

# Handle Angular and Vue.js routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>';

    $htaccessFile = '/home/<USER>/public_html/public/.htaccess';
    
    if (file_put_contents($htaccessFile, $htaccessContent)) {
        echo "✅ Created .htaccess file in public/ directory<br>";
    } else {
        echo "❌ Failed to create .htaccess file<br>";
    }
    
    // Also create a redirect in the root
    $rootHtaccess = '/home/<USER>/public_html/.htaccess';
    $rootRedirect = 'RewriteEngine On
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ /public/$1 [L,R=301]';
    
    if (file_put_contents($rootHtaccess, $rootRedirect)) {
        echo "✅ Created redirect .htaccess in root directory<br>";
    } else {
        echo "❌ Failed to create root .htaccess<br>";
    }
}

?>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
.btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: black; }
.btn-danger { background: #dc3545; }
</style>

<h2>🛠️ Fix Actions</h2>
<p>Choose the actions to fix your document root issue:</p>

<form method="post" style="margin: 20px 0;">
    <button type="submit" name="action" value="create_public_dir" class="btn">📁 Create Public Directory</button>
    <button type="submit" name="action" value="move_files" class="btn">📦 Move Files to Public</button>
    <button type="submit" name="action" value="create_htaccess" class="btn">⚙️ Create .htaccess</button>
    <br><br>
    <button type="submit" name="action" value="full_fix" class="btn btn-success" onclick="return confirm('This will reorganize your files to fix the document root. Continue?')">🚀 FULL FIX (All Actions)</button>
</form>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3>⚠️ What This Will Do:</h3>
    <ul>
        <li>Create a <code>public/</code> subdirectory</li>
        <li>Move your web-accessible files (index.php, assets, uploads) to <code>public/</code></li>
        <li>Create proper .htaccess files for routing</li>
        <li>Your website will then be accessible at the correct path</li>
    </ul>
</div>

<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3>✅ After This Fix:</h3>
    <ul>
        <li>Your website will work properly (no more blank pages)</li>
        <li>All your data will be preserved</li>
        <li>The license bypass will remain active</li>
        <li>The web server will find files in the correct location</li>
    </ul>
</div>
