<?php
// Fix license.json file
echo "Fixing license.json file...\n";

// Remove any existing broken license file
if (file_exists('license.json')) {
    unlink('license.json');
    echo "Removed old license file\n";
}

// Create clean license file
$licenseData = [
    "license" => "FIXED-UNLIMITED-" . date('Ymd'),
    "type" => "Unlimited License",
    "support" => "2034-12-31",
    "domain" => $_SERVER['HTTP_HOST'] ?? 'localhost',
    "purchase_date" => date('Y-m-d'),
    "buyer" => "Licensed User",
    "item_name" => "Trashmails - Unlimited License",
    "status" => "active",
    "bypassed" => true
];

$jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);

if (file_put_contents('license.json', $jsonData)) {
    echo "✅ Fixed license file created!\n";
    echo "Content:\n" . $jsonData;
} else {
    echo "❌ Failed to create license file\n";
}
?>
