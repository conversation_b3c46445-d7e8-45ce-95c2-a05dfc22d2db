<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚀 Laravel License Bypass Tool</h1>";

// Find Laravel root directory
function findLaravelRoot() {
    $possiblePaths = [
        '../',           // Parent directory
        '../../',        // Grandparent directory
        './',            // Current directory
        '../app/',       // Check if app is in parent
    ];
    
    foreach ($possiblePaths as $path) {
        if (file_exists($path . 'app') && 
            file_exists($path . 'bootstrap') && 
            file_exists($path . 'config')) {
            return realpath($path);
        }
    }
    
    return false;
}

$laravelRoot = findLaravelRoot();

if (!$laravelRoot) {
    echo "<div style='color: red;'>❌ Could not find Laravel root directory!</div>";
    echo "<p>Please check your installation structure.</p>";
    exit;
}

echo "<h2>✅ Laravel Root Found: {$laravelRoot}</h2>";

// Check current files
echo "<h2>📋 Current File Status</h2>";
$filesToCheck = [
    'app/Services/InstallService.php',
    'config/lobage.php',
    'app/Helpers/Helper.php',
    '.env',
    'license.json'
];

foreach ($filesToCheck as $file) {
    $fullPath = $laravelRoot . '/' . $file;
    if (file_exists($fullPath)) {
        echo "✅ {$file} - EXISTS<br>";
    } else {
        echo "❌ {$file} - MISSING<br>";
    }
}

// Check if license.json exists in current directory
$currentLicense = './license.json';
$laravelLicense = $laravelRoot . '/license.json';

echo "<h2>🔑 License File Management</h2>";
if (file_exists($currentLicense)) {
    echo "✅ License found in current directory<br>";
    
    // Copy to Laravel root if not exists there
    if (!file_exists($laravelLicense)) {
        if (copy($currentLicense, $laravelLicense)) {
            echo "✅ Copied license to Laravel root<br>";
        } else {
            echo "❌ Failed to copy license to Laravel root<br>";
        }
    } else {
        echo "✅ License already exists in Laravel root<br>";
    }
} else {
    echo "❌ No license file found<br>";
}

// Action buttons
if ($_POST['action'] ?? false) {
    $action = $_POST['action'];
    
    echo "<div style='background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🔄 Executing: {$action}</h3>";
    
    switch ($action) {
        case 'create_license':
            createLicenseFile($laravelRoot);
            break;
            
        case 'bypass_install_service':
            bypassInstallService($laravelRoot);
            break;
            
        case 'bypass_config':
            bypassConfig($laravelRoot);
            break;
            
        case 'add_helpers':
            addHelperFunctions($laravelRoot);
            break;
            
        case 'full_bypass':
            createLicenseFile($laravelRoot);
            bypassInstallService($laravelRoot);
            bypassConfig($laravelRoot);
            addHelperFunctions($laravelRoot);
            updateEnvFile($laravelRoot);
            echo "<h3>🎉 FULL BYPASS COMPLETED!</h3>";
            echo "<p>✅ All bypass modifications have been applied!</p>";
            echo "<p>🔗 Your website should now work with unlimited license.</p>";
            break;
    }
    
    echo "</div>";
}

function createLicenseFile($root) {
    $licenseData = [
        "license" => "LARAVEL-BYPASS-" . strtoupper(uniqid()),
        "type" => "Unlimited License (Laravel Bypass)",
        "support" => date('Y-m-d', strtotime('+10 years')),
        "domain" => $_SERVER['HTTP_HOST'] ?? 'localhost',
        "purchase_date" => date('Y-m-d'),
        "buyer" => "Licensed User",
        "item_name" => "Trashmails - Unlimited License",
        "status" => "active",
        "bypassed" => true,
        "laravel_bypass" => true,
        "created_date" => date('Y-m-d H:i:s')
    ];
    
    $jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);
    $licenseFile = $root . '/license.json';
    
    if (file_put_contents($licenseFile, $jsonData)) {
        echo "✅ Created unlimited license file<br>";
        echo "📄 Location: {$licenseFile}<br>";
        echo "🔑 License Key: " . $licenseData['license'] . "<br>";
    } else {
        echo "❌ Failed to create license file<br>";
    }
}

function bypassInstallService($root) {
    $file = $root . '/app/Services/InstallService.php';
    
    if (!file_exists($file)) {
        echo "❌ InstallService.php not found<br>";
        return;
    }
    
    // Backup original
    $backup = $file . '.backup.' . date('Y-m-d-H-i-s');
    copy($file, $backup);
    echo "✅ Backed up InstallService.php<br>";
    
    $content = file_get_contents($file);
    
    // Find and replace the checkLicense method
    $pattern = '/public function checkLicense\([^}]+\}/s';
    $replacement = 'public function checkLicense($key, $back_up = false)
    {
        // BYPASS: Always return successful license validation
        return [
            \'status\' => true,
            \'message\' => \'License validated successfully (bypassed)\',
            \'data\' => [
                \'license\' => $key ?: \'UNLIMITED-LICENSE-\' . strtoupper(uniqid()),
                \'type\' => \'Unlimited License (Bypassed)\',
                \'support\' => date(\'Y-m-d\', strtotime(\'+10 years\')),
                \'domain\' => url(\'/\'),
                \'purchase_date\' => date(\'Y-m-d\'),
                \'buyer\' => \'Licensed User\',
                \'item_name\' => \'Trashmails - Unlimited License\',
                \'bypassed\' => true
            ]
        ];
    }';
    
    $newContent = preg_replace($pattern, $replacement, $content);
    
    if (file_put_contents($file, $newContent)) {
        echo "✅ Modified InstallService.php with bypass<br>";
    } else {
        echo "❌ Failed to modify InstallService.php<br>";
    }
}

function bypassConfig($root) {
    $file = $root . '/config/lobage.php';
    
    if (!file_exists($file)) {
        echo "❌ lobage.php config not found<br>";
        return;
    }
    
    // Backup original
    $backup = $file . '.backup.' . date('Y-m-d-H-i-s');
    copy($file, $backup);
    echo "✅ Backed up lobage.php<br>";
    
    $content = file_get_contents($file);
    
    // Replace API endpoints
    $content = str_replace(
        '"https://api.lobage.com/api/"',
        '"http://localhost/bypass"',
        $content
    );
    
    $content = str_replace(
        '"https://api2.lobage.com/api/"',
        '"http://localhost/bypass"',
        $content
    );
    
    // Add bypass configuration
    $bypassConfig = '
    
    // BYPASS: License bypass configuration
    \'license_bypass_enabled\' => true,
    \'bypass_all_license_checks\' => true,
    \'unlimited_license_active\' => true,';
    
    $content = str_replace('];', $bypassConfig . "\n];", $content);
    
    if (file_put_contents($file, $content)) {
        echo "✅ Modified lobage.php config with bypass<br>";
    } else {
        echo "❌ Failed to modify lobage.php<br>";
    }
}

function addHelperFunctions($root) {
    $file = $root . '/app/Helpers/Helper.php';
    
    if (!file_exists($file)) {
        echo "❌ Helper.php not found<br>";
        return;
    }
    
    // Backup original
    $backup = $file . '.backup.' . date('Y-m-d-H-i-s');
    copy($file, $backup);
    echo "✅ Backed up Helper.php<br>";
    
    $content = file_get_contents($file);
    
    $helperFunctions = '

// License Bypass Helper Functions
if (!function_exists(\'bypassLicenseCheck\')) {
    function bypassLicenseCheck() {
        return true;
    }
}

if (!function_exists(\'createUnlimitedLicense\')) {
    function createUnlimitedLicense() {
        $licenseData = [
            \'license\' => \'UNLIMITED-LICENSE-\' . strtoupper(uniqid()),
            \'type\' => \'Unlimited License\',
            \'support\' => date(\'Y-m-d\', strtotime(\'+10 years\')),
            \'domain\' => url(\'/\'),
            \'purchase_date\' => date(\'Y-m-d\'),
            \'buyer\' => \'Licensed User\',
            \'item_name\' => \'Trashmails - Unlimited License\',
            \'status\' => \'active\',
            \'bypassed\' => true
        ];
        
        $jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);
        file_put_contents(base_path(\'license.json\'), $jsonData);
        
        return $licenseData;
    }
}
';
    
    // Add before closing PHP tag or at the end
    if (strpos($content, '?>') !== false) {
$content = str_replace('?>', $helperFunctions . "\n?>", $content);
} else {
$content .= $helperFunctions;
}

if (file_put_contents($file, $content)) {
echo "✅ Added bypass helper functions<br>";
} else {
echo "❌ Failed to add helper functions<br>";
}
}

function updateEnvFile($root) {
$envFile = $root . '/.env';

if (file_exists($envFile)) {
$backup = $envFile . '.backup.' . date('Y-m-d-H-i-s');
copy($envFile, $backup);
echo "✅ Backed up .env file<br>";

$content = file_get_contents($envFile);

if (strpos($content, 'LICENSE_BYPASS_ACTIVE') === false) {
$content .= "\nLICENSE_BYPASS_ACTIVE=true\n";
file_put_contents($envFile, $content);
echo "✅ Added LICENSE_BYPASS_ACTIVE to .env<br>";
}
} else {
echo "⚠️ .env file not found<br>";
}
}

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    background: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin: 5px;
}

.btn-success {
    background: #28a745;
}

.btn-warning {
    background: #ffc107;
    color: black;
}

.btn-danger {
    background: #dc3545;
}
</style>

<h2>🛠️ Bypass Actions</h2>
<p>Choose the bypass actions to perform:</p>

<form method="post" style="margin: 20px 0;">
    <button type="submit" name="action" value="create_license" class="btn">🔑 Create License File</button>
    <button type="submit" name="action" value="bypass_install_service" class="btn">⚙️ Bypass Install Service</button>
    <button type="submit" name="action" value="bypass_config" class="btn">🔧 Bypass Config</button>
    <button type="submit" name="action" value="add_helpers" class="btn">🛠️ Add Helper Functions</button>
    <br><br>
    <button type="submit" name="action" value="full_bypass" class="btn btn-success"
        onclick="return confirm('This will apply all bypass modifications. Continue?')">🚀 FULL BYPASS (All
        Actions)</button>
</form>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3>⚠️ Important Notes:</h3>
    <ul>
        <li>All original files will be backed up automatically</li>
        <li>The bypass will preserve all your existing data</li>
        <li>You can rollback using the .backup files if needed</li>
        <li>Test your website after applying the bypass</li>
    </ul>
</div>