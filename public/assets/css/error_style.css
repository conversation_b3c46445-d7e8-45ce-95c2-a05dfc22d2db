@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
:root {
  --primary_color: #793ef1;
  --text_color: #212121;
  --bg_color: #f9f9f9;
  --border_color: #e3e7ed;
  --font-size: 14px;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, Inter, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-height: 100vh;
  min-height: -webkit-fill-available;
  background-color: var(--bg-color);
  color: var(--text_color);
}
body > * {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}
body ::-webkit-scrollbar {
  width: 3px;
}
body ::-webkit-scrollbar-track {
  background: #fff;
}
body ::-webkit-scrollbar-thumb {
  background: #e3e7ed;
}
body > ::-webkit-scrollbar-thumb:hover {
  background: #e3e7ed;
}



.error-page {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    min-height: 100vh;
    max-width: 800px;
    margin-right: auto;
    margin-left: auto;
    padding: 20px;
    text-align: center;
  }

  .error-page-img {
    width: 350px;
    margin-bottom: 45px;
  }
  .error-page-img img {
    width: 100%;
  }

  .error-page-title {
    margin-bottom: 10px;
  }

  .btn.btn-md {
    padding: 10px 28px;
  }

  .btn.btn-primary {
    background-color: var(--primary_color);
    border-color: var(--primary_color);
  }
  .btn.btn-primary:active, .btn.btn-primary:focus, .btn.btn-primary:hover {
    background-color: var(--primary_color);
    border-color: var(--primary_color);
  }

  #svg-color {
    color: var(--primary_color);
}
