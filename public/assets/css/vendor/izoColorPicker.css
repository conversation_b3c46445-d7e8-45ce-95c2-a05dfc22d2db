/* 
    Version     : 1.0
    Released on : 12.02.2021
    Author      : <PERSON>         : https://izocolorpicker.rukovoditel.net/
    License     : MIT License
*/

.izoColorPicker-popup{  
    font-family: Times New Roman;
    position: absolute;
    width: 244px;   
    padding: 10px 10px;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
    box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
    box-sizing: border-box;
    z-index: 1071;
}


/* darw triangle */
.izoColorPicker-popup:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute;    
  left:10px;
}
.izoColorPicker-popup:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  border-top: 0;
  position: absolute;  
  left: 11px;
}

/* triangle orientation */
.izoColorPicker-popup-top:before {
  top: -7px;
}
.izoColorPicker-popup-top:after {
  top: -6px;
}
.izoColorPicker-popup-bottom:before {
  bottom: -7px;
  border-bottom: 0;
  border-top: 7px solid #999;
}
.izoColorPicker-popup-bottom:after {
  bottom: -6px;
  border-bottom: 0;
  border-top: 6px solid #ffffff;
}

/* shift triangle to right corner */
.izoColorPicker-popup-right:before{
    left: auto;
    right: 10px;
}

.izoColorPicker-popup-right:after{
    left: auto;
    right: 11px;
}

.izoColorPicker-popup input:focus{
    outline: none;
}

.izoColorPicker-input-clearable{
    position: relative;
    display: inline-block;
    float:left;
}

.izoColorPicker-icon-clear{    
    position: absolute;
    right: 13px;
    top: 0;
    padding: 0px 8px 5px 3px;
    font-style: normal;
    font-size: 20px;
    user-select: none;
    cursor: pointer;
    box-sizing: border-box;
    font-weight: 400;
    line-height: 1.5;    
}

.izoColorPicker-row{
    padding-bottom: 10px;
}

.izoColorPicker-popup input.izoColorPicker-input-color{
    display: inline-block;
    vertical-align: top;
    margin-right: 13px;
    width: 100px;
    border: 1px solid #d5d5d5;
    background-color: #fff;
    height: 32px;
    padding: 0 5px;
    color: #111;
    font-size: 15px;
    font-family: Arial;
    box-sizing: border-box;
    
}

.izoColorPicker-color-preview{
    display: inline-block;
    width: 83px;
    border: 1px solid #d5d5d5;
    background-color: #fff;
    height: 32px;     
    box-sizing: border-box;
    cursor: pointer;
}

.izoColorPicker-9c-item,
.izoColorPicker-sc-item{
    display: inline-block;
    vertical-align: top;
    position: relative;
    cursor: pointer;
    -webkit-transition: all .3s;
    transition: all .3s;
    height: 26px;
    width: -webkit-calc(11.11% - 1px);
    width: calc(11.11% - 1px);
    margin-right: 1px;
    text-align:center;
    box-sizing: border-box;
}
.izoColorPicker-sc-item{
    margin-bottom: 1px;
}

.izoColorPicker-9c-item-current:before,
.izoColorPicker-sc-item-current:before{
    content: "✓";
    color: white;    
    font-size: 18px;
}

.izoColorPicker-8c-item{
    display: inline-block;
    vertical-align: top;
    position: relative;
    cursor: pointer;
    -webkit-transition: all .3s;
    transition: all .3s;
    height: 26px;
    width: -webkit-calc(25% - 1px);
    width: calc(25% - 1px);
    margin-right: 1px;
    text-align:center;
    margin-bottom: 1px;
}

.izoColorPicker-8c-item-current:before {
    content: "✓";
    color: white;    
    font-size: 18px;
}

.izoColorPicker-buttons{
    text-align: center;
    font-family: Arial;
    margin:0;
}

.izoColorPicker-button{        
    position: relative;
    outline: 0;
    cursor: pointer;
    text-align: center;
    display: inline-block;
    vertical-align: top;
    font-size: 14px;
    font-weight: 500;
    padding: 5px 5px;    
    min-width: 90px;
    color: #555;        
    border-radius: 4px;
    border: 0;
    background: #DBDBDB;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.izoColorPicker-button-apply{
    color: #fff;
    background: #626262;
    margin-right: 15px;
}

.izoColorPicker-save-color{
    height: 32px;
    vertical-align: top;
    border: 1px solid #d5d5d5;
    border-left: 0;
    cursor: pointer;
    width: 25px;
    outline: 0;
    font-size: 18px;
    color: black;
    box-sizing: border-box;
    font-family: Times New Roman;
}

.izoColorPicker-remove-color{
    height: 32px;
    vertical-align: top;
    border: 1px solid #d5d5d5;
    border-left: 0;
    cursor: pointer;
    width: 25px;
    outline: 0;
    font-size: 18px;
    color:white;
    background-color: #757575;
    box-sizing: border-box;
    font-family: Times New Roman;
}


