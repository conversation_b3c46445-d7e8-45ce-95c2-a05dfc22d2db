!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i(require("@flasher/flasher")):"function"==typeof define&&define.amd?define(["@flasher/flasher"],i):((t="undefined"!=typeof globalThis?globalThis:t||self).flasher=t.flasher||{},t.flasher.notyf=i(t.flasher))}(this,(function(t){"use strict";var i,n=function(){return n=Object.assign||function(t){for(var i,n=1,e=arguments.length;n<e;n++)for(var o in i=arguments[n])Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},n.apply(this,arguments)},e=function(){return e=Object.assign||function(t){for(var i,n=1,e=arguments.length;n<e;n++)for(var o in i=arguments[n])Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},e.apply(this,arguments)},o=function(){function t(t){this.options=t,this.listeners={}}return t.prototype.on=function(t,i){var n=this.listeners[t]||[];this.listeners[t]=n.concat([i])},t.prototype.triggerEvent=function(t,i){var n=this;(this.listeners[t]||[]).forEach((function(t){return t({target:n,event:i})}))},t}();!function(t){t[t.Add=0]="Add",t[t.Remove=1]="Remove"}(i||(i={}));var s,a=function(){function t(){this.notifications=[]}return t.prototype.push=function(t){this.notifications.push(t),this.updateFn(t,i.Add,this.notifications)},t.prototype.splice=function(t,n){var e=this.notifications.splice(t,n)[0];return this.updateFn(e,i.Remove,this.notifications),e},t.prototype.indexOf=function(t){return this.notifications.indexOf(t)},t.prototype.onUpdate=function(t){this.updateFn=t},t}();!function(t){t.Dismiss="dismiss",t.Click="click"}(s||(s={}));var r={types:[{type:"success",className:"notyf__toast--success",backgroundColor:"#3dc763",icon:{className:"notyf__icon--success",tagName:"i"}},{type:"error",className:"notyf__toast--error",backgroundColor:"#ed3d3d",icon:{className:"notyf__icon--error",tagName:"i"}}],duration:2e3,ripple:!0,position:{x:"right",y:"bottom"},dismissible:!1},c=function(){function t(){this.notifications=[],this.events={},this.X_POSITION_FLEX_MAP={left:"flex-start",center:"center",right:"flex-end"},this.Y_POSITION_FLEX_MAP={top:"flex-start",center:"center",bottom:"flex-end"};var t=document.createDocumentFragment(),i=this._createHTMLElement({tagName:"div",className:"notyf"});t.appendChild(i),document.body.appendChild(t),this.container=i,this.animationEndEventName=this._getAnimationEndEventName(),this._createA11yContainer()}return t.prototype.on=function(t,i){var n;this.events=e(e({},this.events),((n={})[t]=i,n))},t.prototype.update=function(t,n){n===i.Add?this.addNotification(t):n===i.Remove&&this.removeNotification(t)},t.prototype.removeNotification=function(t){var i,n,e=this,o=this._popRenderedNotification(t);o&&((i=o.node).classList.add("notyf__toast--disappear"),i.addEventListener(this.animationEndEventName,n=function(t){t.target===i&&(i.removeEventListener(e.animationEndEventName,n),e.container.removeChild(i))}))},t.prototype.addNotification=function(t){var i=this._renderNotification(t);this.notifications.push({notification:t,node:i}),this._announce(t.options.message||"Notification")},t.prototype._renderNotification=function(t){var i,n=this._buildNotificationCard(t),e=t.options.className;return e&&(i=n.classList).add.apply(i,e.split(" ")),this.container.appendChild(n),n},t.prototype._popRenderedNotification=function(t){for(var i=-1,n=0;n<this.notifications.length&&i<0;n++)this.notifications[n].notification===t&&(i=n);if(-1!==i)return this.notifications.splice(i,1)[0]},t.prototype.getXPosition=function(t){var i;return(null===(i=null==t?void 0:t.position)||void 0===i?void 0:i.x)||"right"},t.prototype.getYPosition=function(t){var i;return(null===(i=null==t?void 0:t.position)||void 0===i?void 0:i.y)||"bottom"},t.prototype.adjustContainerAlignment=function(t){var i=this.X_POSITION_FLEX_MAP[this.getXPosition(t)],n=this.Y_POSITION_FLEX_MAP[this.getYPosition(t)],e=this.container.style;e.setProperty("justify-content",n),e.setProperty("align-items",i)},t.prototype._buildNotificationCard=function(t){var i=this,n=t.options,e=n.icon;this.adjustContainerAlignment(n);var o=this._createHTMLElement({tagName:"div",className:"notyf__toast"}),a=this._createHTMLElement({tagName:"div",className:"notyf__ripple"}),r=this._createHTMLElement({tagName:"div",className:"notyf__wrapper"}),c=this._createHTMLElement({tagName:"div",className:"notyf__message"});c.innerHTML=n.message||"";var p=n.background||n.backgroundColor;if(e){var f=this._createHTMLElement({tagName:"div",className:"notyf__icon"});if(("string"==typeof e||e instanceof String)&&(f.innerHTML=new String(e).valueOf()),"object"==typeof e){var l=e.tagName,u=void 0===l?"i":l,d=e.className,h=e.text,y=e.color,m=void 0===y?p:y,v=this._createHTMLElement({tagName:u,className:d,text:h});m&&(v.style.color=m),f.appendChild(v)}r.appendChild(f)}if(r.appendChild(c),o.appendChild(r),p&&(n.ripple?(a.style.background=p,o.appendChild(a)):o.style.background=p),n.dismissible){var g=this._createHTMLElement({tagName:"div",className:"notyf__dismiss"}),_=this._createHTMLElement({tagName:"button",className:"notyf__dismiss-btn"});g.appendChild(_),r.appendChild(g),o.classList.add("notyf__toast--dismissible"),_.addEventListener("click",(function(n){var e,o;null===(o=(e=i.events)[s.Dismiss])||void 0===o||o.call(e,{target:t,event:n}),n.stopPropagation()}))}o.addEventListener("click",(function(n){var e,o;return null===(o=(e=i.events)[s.Click])||void 0===o?void 0:o.call(e,{target:t,event:n})}));var N="top"===this.getYPosition(n)?"upper":"lower";return o.classList.add("notyf__toast--"+N),o},t.prototype._createHTMLElement=function(t){var i=t.tagName,n=t.className,e=t.text,o=document.createElement(i);return n&&(o.className=n),o.textContent=e||null,o},t.prototype._createA11yContainer=function(){var t=this._createHTMLElement({tagName:"div",className:"notyf-announcer"});t.setAttribute("aria-atomic","true"),t.setAttribute("aria-live","polite"),t.style.border="0",t.style.clip="rect(0 0 0 0)",t.style.height="1px",t.style.margin="-1px",t.style.overflow="hidden",t.style.padding="0",t.style.position="absolute",t.style.width="1px",t.style.outline="0",document.body.appendChild(t),this.a11yContainer=t},t.prototype._announce=function(t){var i=this;this.a11yContainer.textContent="",setTimeout((function(){i.a11yContainer.textContent=t}),100)},t.prototype._getAnimationEndEventName=function(){var t,i=document.createElement("_fake"),n={MozTransition:"animationend",OTransition:"oAnimationEnd",WebkitTransition:"webkitAnimationEnd",transition:"animationend"};for(t in n)if(void 0!==i.style[t])return n[t];return"animationend"},t}(),p=function(){function t(t){var i=this;this.dismiss=this._removeNotification,this.notifications=new a,this.view=new c;var n=this.registerTypes(t);this.options=e(e({},r),t),this.options.types=n,this.notifications.onUpdate((function(t,n){return i.view.update(t,n)})),this.view.on(s.Dismiss,(function(t){var n=t.target,e=t.event;i._removeNotification(n),n.triggerEvent(s.Dismiss,e)})),this.view.on(s.Click,(function(t){var i=t.target,n=t.event;return i.triggerEvent(s.Click,n)}))}return t.prototype.error=function(t){var i=this.normalizeOptions("error",t);return this.open(i)},t.prototype.success=function(t){var i=this.normalizeOptions("success",t);return this.open(i)},t.prototype.open=function(t){var i=this.options.types.find((function(i){return i.type===t.type}))||{},n=e(e({},i),t);this.assignProps(["ripple","position","dismissible"],n);var s=new o(n);return this._pushNotification(s),s},t.prototype.dismissAll=function(){for(;this.notifications.splice(0,1););},t.prototype.assignProps=function(t,i){var n=this;t.forEach((function(t){i[t]=null==i[t]?n.options[t]:i[t]}))},t.prototype._pushNotification=function(t){var i=this;this.notifications.push(t);var n=void 0!==t.options.duration?t.options.duration:this.options.duration;n&&setTimeout((function(){return i._removeNotification(t)}),n)},t.prototype._removeNotification=function(t){var i=this.notifications.indexOf(t);-1!==i&&this.notifications.splice(i,1)},t.prototype.normalizeOptions=function(t,i){var n={type:t};return"string"==typeof i?n.message=i:"object"==typeof i&&(n=e(e({},n),i)),n},t.prototype.registerTypes=function(t){var i=(t&&t.types||[]).slice();return r.types.map((function(t){var n=-1;i.forEach((function(i,e){i.type===t.type&&(n=e)}));var o=-1!==n?i.splice(n,1)[0]:{};return e(e({},t),o)})).concat(i)},t}(),f=new(function(){function t(){}return t.prototype.success=function(t,i,n){this.flash("success",t,i,n)},t.prototype.info=function(t,i,n){this.flash("info",t,i,n)},t.prototype.warning=function(t,i,n){this.flash("warning",t,i,n)},t.prototype.error=function(t,i,n){this.flash("error",t,i,n)},t.prototype.flash=function(t,i,n,e){var o=this.createNotification(t,i,n,e);this.renderOptions({}),this.render({notification:o})},t.prototype.createNotification=function(t,i,n,e){if("object"==typeof t?(t=(e=t).type,i=e.message,n=e.title):"object"==typeof i?(i=(e=i).message,n=e.title):"object"==typeof n&&(n=(e=n).title),void 0===i)throw new Error("message option is required");return{type:t||"info",message:i,title:n,options:e}},t.prototype.render=function(t){var i=t.notification;i.type=i.type||"info";var e=n(n({},i),i.options);this.notyf=this.notyf||new p,this.notyf.open(e),this.notyf.view.container.dataset.turboCache="false",this.notyf.view.container.classList.add("fl-no-cache"),this.notyf.view.a11yContainer.dataset.turboCache="false",this.notyf.view.a11yContainer.classList.add("fl-no-cache")},t.prototype.renderOptions=function(t){var i=n({duration:t.duration||5e3},t);i.types=i.types||[],i.types.push({type:"info",className:"notyf__toast--info",background:"#5784E5",icon:{className:"notyf__icon--info",tagName:"i"}}),i.types.push({type:"warning",className:"notyf__toast--warning",background:"#E3A008",icon:{className:"notyf__icon--warning",tagName:"i"}}),(!this.notyf||this.notyf.view&&!this.notyf.view.container.parentNode)&&(this.notyf=new p(i))},t}());return t.addFactory("notyf",f),f}));
