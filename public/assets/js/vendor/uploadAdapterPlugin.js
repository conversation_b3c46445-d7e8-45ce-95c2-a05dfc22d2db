class ImgurUploadAdapter{constructor(e){this.loader=e}isImage(e){return["image/png","image/jpg","image/jpeg","image/gif"].includes(e.type)}upload(){return this.loader.file.then(e=>{if(!this.isImage(e))throw Error("Invalid file type. Only image files are allowed (JPEG, JPG, PNG, GIF).");let t=new FormData;return t.append("image",e),fetch("https://api.imgur.com/3/image",{method:"POST",headers:{Authorization:"Client-ID YOUR ID HERE "},body:t}).then(e=>{if(e.ok)return e.json();throw Error("Failed to upload the image")}).then(e=>{if(e.success)return{default:e.data.link};throw Error("Failed to upload the image")})}).catch(e=>{throw e})}abort(){this.xhr&&this.xhr.abort()}response(){return{default:this.default}}destroy(){this.xhr&&(this.xhr.removeEventListener("abort",this._boundAbort),this.xhr.removeEventListener("error",this._boundError),this.xhr.removeEventListener("load",this._boundLoad),this.xhr.removeEventListener("progress",this._boundProgress),this.xhr=null)}}
