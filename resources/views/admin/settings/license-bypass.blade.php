@extends('admin.layouts.admin')
@section('title', 'License Bypass Management')
@section('content')
    <!-- Settings -->
    <div class="settings">
        @include('admin.partials.settings')
        <!-- Settings Content -->
        <div class="settings-content w-100">
            <div class="box">
                <h5 class="mb-4">{{ __('License Bypass Management') }}</h5>
                <p class="text-muted mb-4">{{ __('Manage license bypass settings and create unlimited licenses.') }}</p>

                @if(session('success'))
                    <div class="alert alert-success alert-dismissible">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if($errors->any())
                    <div class="alert alert-danger alert-dismissible">
                        @foreach($errors->all() as $error)
                            {{ $error }}<br>
                        @endforeach
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Current License Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">{{ __('Current License Status') }}</h6>
                    </div>
                    <div class="card-body">
                        @if($licenseExists)
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('License Key') }}</label>
                                    <input type="text" class="form-control" value="{{ $licenseData['license'] ?? 'N/A' }}" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('License Type') }}</label>
                                    <input type="text" class="form-control" value="{{ $licenseData['type'] ?? 'N/A' }}" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('Support Until') }}</label>
                                    <input type="text" class="form-control" value="{{ $licenseData['support'] ?? 'N/A' }}" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('Buyer') }}</label>
                                    <input type="text" class="form-control" value="{{ $licenseData['buyer'] ?? 'N/A' }}" readonly>
                                </div>
                                @if(isset($licenseData['bypassed']) && $licenseData['bypassed'])
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i> {{ __('This license is bypassed and unlimited.') }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> {{ __('No license file found.') }}
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Installation Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">{{ __('Installation Status') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            @foreach($installationStatus as $key => $value)
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>{{ str_replace('_', ' ', $key) }}</span>
                                        <span class="badge {{ $value == '1' ? 'bg-success' : 'bg-danger' }}">
                                            {{ $value == '1' ? 'Complete' : 'Pending' }}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Quick Bypass Activation -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">{{ __('Quick Bypass Activation') }}</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">{{ __('Activate unlimited license bypass with one click.') }}</p>
                        <form action="{{ route('admin.settings.license-bypass.activate') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-unlock"></i> {{ __('Activate Unlimited License') }}
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Custom License Creation -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">{{ __('Create Custom License') }}</h6>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.settings.license-bypass.custom') }}" method="POST">
                            @csrf
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('License Key') }}</label>
                                    <input type="text" name="license_key" class="form-control" 
                                           value="CUSTOM-{{ strtoupper(uniqid()) }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('License Type') }}</label>
                                    <select name="license_type" class="form-control" required>
                                        <option value="Unlimited License">Unlimited License</option>
                                        <option value="Extended License">Extended License</option>
                                        <option value="Regular License">Regular License</option>
                                        <option value="Developer License">Developer License</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('Buyer Name') }}</label>
                                    <input type="text" name="buyer_name" class="form-control" 
                                           value="Licensed User" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('Support Years') }}</label>
                                    <input type="number" name="support_years" class="form-control" 
                                           value="10" min="1" max="50" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> {{ __('Create Custom License') }}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Reset Installation -->
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">{{ __('Reset Installation') }}</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">{{ __('Reset all installation flags and remove license file. Use with caution!') }}</p>
                        <form action="{{ route('admin.settings.license-bypass.reset') }}" method="POST" 
                              onsubmit="return confirm('Are you sure you want to reset the installation? This will require reinstalling the system.')">
                            @csrf
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-redo"></i> {{ __('Reset Installation') }}
                            </button>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection
