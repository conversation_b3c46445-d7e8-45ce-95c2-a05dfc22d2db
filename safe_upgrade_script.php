<?php
/**
 * Safe Upgrade Script for Existing Installation
 * This script upgrades your existing system with bypass features while preserving all data
 * 
 * Usage: php safe_upgrade_script.php
 */

echo "=== Trashmails Safe Upgrade to Bypass Version ===\n";
echo "This will upgrade your existing installation with bypass features\n";
echo "All your data (users, emails, blogs, etc.) will be preserved\n\n";

// Function to backup a file
function backupFile($filePath) {
    if (file_exists($filePath)) {
        $backupPath = $filePath . '.backup.' . date('Y-m-d-H-i-s');
        if (copy($filePath, $backupPath)) {
            echo "✓ Backed up: {$filePath} → {$backupPath}\n";
            return $backupPath;
        } else {
            echo "❌ Failed to backup: {$filePath}\n";
            return false;
        }
    }
    return null;
}

// Function to update .env file safely
function updateEnvFileSafe($key, $value) {
    $envFile = '.env';
    
    if (!file_exists($envFile)) {
        echo "❌ Error: .env file not found!\n";
        return false;
    }
    
    // Backup .env first
    backupFile($envFile);
    
    $currentEnv = file_get_contents($envFile);
    $value = '"' . trim($value) . '"';
    $pattern = "/^{$key}=(.*)$/m";
    
    if (preg_match($pattern, $currentEnv, $matches)) {
        $newEnv = preg_replace($pattern, "{$key}={$value}", $currentEnv);
        echo "✓ Updated {$key} from {$matches[1]} to {$value}\n";
    } else {
        $newEnv = $currentEnv . "\n{$key}={$value}";
        echo "✓ Added {$key} = {$value}\n";
    }
    
    file_put_contents($envFile, $newEnv);
    return true;
}

// Function to create unlimited license while preserving existing data
function createSafeLicense() {
    $licenseFile = 'license.json';
    $existingLicense = null;
    
    // Check if license already exists
    if (file_exists($licenseFile)) {
        echo "📄 Existing license found, backing up...\n";
        backupFile($licenseFile);
        $existingLicense = json_decode(file_get_contents($licenseFile), true);
    }
    
    // Create new unlimited license, preserving some existing data if available
    $licenseData = [
        'license' => $existingLicense['license'] ?? 'UPGRADED-UNLIMITED-' . strtoupper(uniqid()),
        'type' => 'Unlimited License (Upgraded)',
        'support' => date('Y-m-d', strtotime('+10 years')),
        'domain' => $existingLicense['domain'] ?? ($_SERVER['HTTP_HOST'] ?? 'localhost'),
        'purchase_date' => $existingLicense['purchase_date'] ?? date('Y-m-d'),
        'buyer' => $existingLicense['buyer'] ?? 'Licensed User',
        'item_name' => 'Trashmails - Unlimited License (Upgraded)',
        'status' => 'active',
        'bypassed' => true,
        'upgraded' => true,
        'upgrade_date' => date('Y-m-d H:i:s'),
        'original_license' => $existingLicense
    ];
    
    $jsonData = json_encode($licenseData, JSON_PRETTY_PRINT);
    file_put_contents($licenseFile, $jsonData);
    
    echo "✓ Created unlimited license (preserving original data)\n";
    echo "  License Key: " . $licenseData['license'] . "\n";
    echo "  Support Until: " . $licenseData['support'] . "\n";
    
    return $licenseData;
}

try {
    echo "Step 1: Creating backups...\n";
    
    // Backup critical files
    $filesToBackup = [
        'app/Services/InstallService.php',
        'app/Http/Controllers/Admin/Settings/LicenseController.php',
        'app/Http/Controllers/InstallController.php',
        'config/lobage.php',
        'app/Helpers/Helper.php',
        'routes/admin.php',
        'bootstrap/app.php'
    ];
    
    foreach ($filesToBackup as $file) {
        if (file_exists($file)) {
            backupFile($file);
        }
    }
    
    echo "\nStep 2: Checking current installation status...\n";
    
    // Check current status
    $currentStatus = [
        'SYSTEM_INSTALLED' => env('SYSTEM_INSTALLED', '0'),
        'INSTALL_LICENSE' => env('INSTALL_LICENSE', '0'),
        'DB_CONNECTION' => env('DB_CONNECTION', 'none'),
        'APP_URL' => env('APP_URL', 'none')
    ];
    
    foreach ($currentStatus as $key => $value) {
        echo "  {$key}: {$value}\n";
    }
    
    if ($currentStatus['SYSTEM_INSTALLED'] !== '1') {
        echo "⚠️  Warning: System appears not fully installed. Proceeding with caution...\n";
    }
    
    echo "\nStep 3: Activating bypass features...\n";
    
    // Only update license-related flags, preserve others
    updateEnvFileSafe('INSTALL_LICENSE', '1');
    updateEnvFileSafe('LICENSE_BYPASS_ACTIVE', 'true');
    
    // Ensure system is marked as installed
    if ($currentStatus['SYSTEM_INSTALLED'] !== '1') {
        updateEnvFileSafe('SYSTEM_INSTALLED', '1');
    }
    
    echo "\nStep 4: Creating/upgrading license...\n";
    $license = createSafeLicense();
    
    echo "\nStep 5: Clearing caches safely...\n";
    
    // Clear caches without affecting data
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "✓ OPCache cleared\n";
    }
    
    // Try Laravel cache clear (safe operations only)
    if (file_exists('artisan')) {
        $commands = ['cache:clear', 'config:clear', 'route:clear', 'view:clear'];
        foreach ($commands as $command) {
            $output = [];
            $return_var = 0;
            exec("php artisan {$command} 2>&1", $output, $return_var);
            if ($return_var === 0) {
                echo "✓ Laravel {$command} completed\n";
            }
        }
    }
    
    echo "\n=== SAFE UPGRADE COMPLETE ===\n";
    echo "✅ Your existing installation has been upgraded with bypass features!\n";
    echo "✅ All your data (users, emails, blogs, etc.) has been preserved\n";
    echo "✅ License bypass is now active with unlimited access\n\n";
    
    echo "What was preserved:\n";
    echo "- All database data (users, emails, messages, blogs, etc.)\n";
    echo "- All uploaded files and attachments\n";
    echo "- All configuration settings (except license-related)\n";
    echo "- All themes and customizations\n";
    echo "- All cache and session data\n\n";
    
    echo "What was upgraded:\n";
    echo "- License system now bypassed\n";
    echo "- Unlimited access to all features\n";
    echo "- New admin panel options for license management\n\n";
    
    echo "Next Steps:\n";
    echo "1. Test your website to ensure everything works\n";
    echo "2. Access admin panel: /admin\n";
    echo "3. Check Settings > License Bypass for new options\n";
    echo "4. All your existing data should be intact\n\n";
    
    echo "Backup files created (in case you need to revert):\n";
    $backupFiles = glob('*.backup.*');
    foreach ($backupFiles as $backup) {
        echo "- {$backup}\n";
    }
    
} catch (Exception $e) {
    echo "\n❌ ERROR during upgrade: " . $e->getMessage() . "\n";
    echo "Your original files are backed up. Check the .backup files to restore if needed.\n";
    exit(1);
}

echo "\n=== Safe Upgrade completed successfully ===\n";
?>
