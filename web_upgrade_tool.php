<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trashmails License Bypass Upgrade Tool</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Trashmails License Bypass Upgrade Tool</h1>
            <p>Safe upgrade for existing installations - preserves all your data</p>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            echo '<div class="log" id="upgradeLog">';
            
            if ($action === 'check_status') {
                checkCurrentStatus();
            } elseif ($action === 'upgrade') {
                performUpgrade();
            } elseif ($action === 'create_license') {
                createLicenseOnly();
            }
            
            echo '</div>';
        } else {
            showUpgradeInterface();
        }

        function checkCurrentStatus() {
            echo "=== CHECKING CURRENT INSTALLATION STATUS ===\n\n";
            
            // Check .env file
            if (file_exists('.env')) {
                echo "✓ .env file found\n";
                $envContent = file_get_contents('.env');
                
                $checks = [
                    'SYSTEM_INSTALLED' => 'System Installation',
                    'INSTALL_LICENSE' => 'License Installation',
                    'DB_CONNECTION' => 'Database Connection',
                    'APP_URL' => 'Application URL'
                ];
                
                foreach ($checks as $key => $label) {
                    if (preg_match("/^{$key}=(.*)$/m", $envContent, $matches)) {
                        $value = trim($matches[1], '"');
                        echo "  {$label}: {$value}\n";
                    } else {
                        echo "  {$label}: Not set\n";
                    }
                }
            } else {
                echo "❌ .env file not found\n";
            }
            
            // Check license file
            if (file_exists('license.json')) {
                echo "\n✓ License file exists\n";
                $license = json_decode(file_get_contents('license.json'), true);
                if ($license) {
                    echo "  License Type: " . ($license['type'] ?? 'Unknown') . "\n";
                    echo "  Status: " . ($license['status'] ?? 'Unknown') . "\n";
                    echo "  Bypassed: " . (isset($license['bypassed']) ? 'Yes' : 'No') . "\n";
                }
            } else {
                echo "\n⚠️  No license file found\n";
            }
            
            // Check critical files
            $criticalFiles = [
                'app/Services/InstallService.php' => 'Install Service',
                'config/lobage.php' => 'Configuration File',
                'app/Helpers/Helper.php' => 'Helper Functions'
            ];
            
            echo "\n=== CRITICAL FILES CHECK ===\n";
            foreach ($criticalFiles as $file => $name) {
                if (file_exists($file)) {
                    echo "✓ {$name}: Found\n";
                } else {
                    echo "❌ {$name}: Missing\n";
                }
            }
            
            // Check database connection
            echo "\n=== DATABASE CHECK ===\n";
            try {
                if (file_exists('.env')) {
                    $envContent = file_get_contents('.env');
                    if (preg_match("/^DB_CONNECTION=(.*)$/m", $envContent, $matches)) {
                        echo "✓ Database configured: " . trim($matches[1], '"') . "\n";
                    }
                }
            } catch (Exception $e) {
                echo "⚠️  Could not check database: " . $e->getMessage() . "\n";
            }
            
            echo "\n=== STATUS CHECK COMPLETE ===\n";
        }
        
        function performUpgrade() {
            echo "=== STARTING SAFE UPGRADE PROCESS ===\n\n";
            
            try {
                // Step 1: Backup files
                echo "Step 1: Creating backups...\n";
                $filesToBackup = [
                    'app/Services/InstallService.php',
                    'config/lobage.php',
                    'app/Helpers/Helper.php',
                    '.env'
                ];
                
                foreach ($filesToBackup as $file) {
                    if (file_exists($file)) {
                        $backup = $file . '.backup.' . date('Y-m-d-H-i-s');
                        if (copy($file, $backup)) {
                            echo "✓ Backed up: {$file}\n";
                        }
                    }
                }
                
                // Step 2: Update .env
                echo "\nStep 2: Updating environment variables...\n";
                if (file_exists('.env')) {
                    $envContent = file_get_contents('.env');
                    
                    // Add license bypass flag
                    if (strpos($envContent, 'LICENSE_BYPASS_ACTIVE') === false) {
                        $envContent .= "\nLICENSE_BYPASS_ACTIVE=\"true\"\n";
                        file_put_contents('.env', $envContent);
                        echo "✓ Added LICENSE_BYPASS_ACTIVE flag\n";
                    }
                    
                    // Ensure system is installed
                    if (preg_match("/^SYSTEM_INSTALLED=(.*)$/m", $envContent, $matches)) {
                        $value = trim($matches[1], '"');
                        if ($value !== '1') {
                            $envContent = preg_replace("/^SYSTEM_INSTALLED=(.*)$/m", 'SYSTEM_INSTALLED="1"', $envContent);
                            file_put_contents('.env', $envContent);
                            echo "✓ Updated SYSTEM_INSTALLED to 1\n";
                        }
                    }
                }
                
                // Step 3: Create unlimited license
                echo "\nStep 3: Creating unlimited license...\n";
                $licenseData = [
                    'license' => 'WEB-UPGRADE-' . strtoupper(uniqid()),
                    'type' => 'Unlimited License (Web Upgrade)',
                    'support' => date('Y-m-d', strtotime('+10 years')),
                    'domain' => $_SERVER['HTTP_HOST'] ?? 'localhost',
                    'purchase_date' => date('Y-m-d'),
                    'buyer' => 'Licensed User',
                    'item_name' => 'Trashmails - Unlimited License',
                    'status' => 'active',
                    'bypassed' => true,
                    'web_upgrade' => true,
                    'upgrade_date' => date('Y-m-d H:i:s')
                ];
                
                file_put_contents('license.json', json_encode($licenseData, JSON_PRETTY_PRINT));
                echo "✓ Created unlimited license file\n";
                echo "  License Key: " . $licenseData['license'] . "\n";
                
                // Step 4: Clear caches
                echo "\nStep 4: Clearing caches...\n";
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                    echo "✓ OPCache cleared\n";
                }
                
                echo "\n=== UPGRADE COMPLETED SUCCESSFULLY ===\n";
                echo "✅ Your system has been upgraded with bypass features!\n";
                echo "✅ All your existing data has been preserved\n";
                echo "✅ License bypass is now active\n\n";
                echo "Next steps:\n";
                echo "1. Test your website functionality\n";
                echo "2. Access your admin panel\n";
                echo "3. All features are now unlimited\n";
                
            } catch (Exception $e) {
                echo "\n❌ ERROR during upgrade: " . $e->getMessage() . "\n";
                echo "Check backup files to restore if needed.\n";
            }
        }
        
        function createLicenseOnly() {
            echo "=== CREATING UNLIMITED LICENSE ONLY ===\n\n";
            
            $licenseData = [
                'license' => 'QUICK-LICENSE-' . strtoupper(uniqid()),
                'type' => 'Unlimited License (Quick Creation)',
                'support' => date('Y-m-d', strtotime('+10 years')),
                'domain' => $_SERVER['HTTP_HOST'] ?? 'localhost',
                'purchase_date' => date('Y-m-d'),
                'buyer' => 'Licensed User',
                'item_name' => 'Trashmails - Unlimited License',
                'status' => 'active',
                'bypassed' => true,
                'quick_creation' => true,
                'created_date' => date('Y-m-d H:i:s')
            ];
            
            file_put_contents('license.json', json_encode($licenseData, JSON_PRETTY_PRINT));
            echo "✅ Unlimited license created successfully!\n";
            echo "License Key: " . $licenseData['license'] . "\n";
            echo "Support Until: " . $licenseData['support'] . "\n";
        }
        
        function showUpgradeInterface() {
        ?>
        
        <div class="step">
            <h3>📋 Step 1: Check Current Status</h3>
            <p>First, let's check your current installation status to ensure safe upgrade.</p>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="check_status">
                <button type="submit" class="btn">Check Installation Status</button>
            </form>
        </div>
        
        <div class="step">
            <h3>🚀 Step 2: Perform Safe Upgrade</h3>
            <p>This will upgrade your system with bypass features while preserving all your data.</p>
            <div class="warning">
                <strong>⚠️ Important:</strong> This will modify system files but preserve all your data (users, emails, blogs, etc.)
            </div>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="upgrade">
                <button type="submit" class="btn btn-success" onclick="return confirm('Are you sure you want to upgrade? All your data will be preserved.')">Start Safe Upgrade</button>
            </form>
        </div>
        
        <div class="step">
            <h3>🔑 Step 3: Quick License Creation (Alternative)</h3>
            <p>If you only need to create an unlimited license without other modifications.</p>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="create_license">
                <button type="submit" class="btn">Create Unlimited License Only</button>
            </form>
        </div>
        
        <div class="info">
            <h4>🛡️ What This Upgrade Does:</h4>
            <ul>
                <li><strong>Preserves ALL your data:</strong> Users, emails, blogs, settings, files</li>
                <li><strong>Removes license restrictions:</strong> Unlimited access to all features</li>
                <li><strong>Creates backups:</strong> All modified files are backed up automatically</li>
                <li><strong>Safe and reversible:</strong> Can be undone if needed</li>
            </ul>
        </div>
        
        <div class="warning">
            <h4>⚠️ Before You Start:</h4>
            <ul>
                <li>Make sure you have cPanel/FTP access in case of issues</li>
                <li>Consider creating a full website backup (optional but recommended)</li>
                <li>The upgrade process is designed to be safe, but backups are always good practice</li>
            </ul>
        </div>
        
        <?php } ?>
    </div>
</body>
</html>
